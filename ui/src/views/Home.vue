<template>
  <!-- Admin View -->
  <div v-if="isAdmin">
    <!-- Stats Row -->
    <div class="stats-header">
      <h2 class="stats-title">系统概览</h2>
    </div>
    <div class="stats-row">
      <div class="stat-card" :class="{ 'loading': loading, 'error': loadError }">
        <div class="stat-title">用户总数</div>
        <div v-if="loading" class="stat-value loading-placeholder"></div>
        <div v-else-if="loadError" class="stat-value error-text">加载失败</div>
        <div v-else class="stat-value">{{ stats.users.total || 0 }}</div>
        <div v-if="loading" class="stat-desc loading-placeholder"></div>
        <div v-else-if="loadError" class="stat-desc error-text">请刷新重试</div>
        <div v-else class="stat-desc">近7天新增{{ stats.users.new_last_7_days || 0 }}人</div>
      </div>
      <div class="stat-card" :class="{ 'loading': loading, 'error': loadError }">
        <div class="stat-title">项目总数</div>
        <div v-if="loading" class="stat-value loading-placeholder"></div>
        <div v-else-if="loadError" class="stat-value error-text">加载失败</div>
        <div v-else class="stat-value">{{ stats.projects.total || 0 }}</div>
        <div v-if="loading" class="stat-desc loading-placeholder"></div>
        <div v-else-if="loadError" class="stat-desc error-text">请刷新重试</div>
        <div v-else class="stat-desc">近7天新增{{ stats.projects.new_last_7_days || 0 }}个</div>
      </div>
      <div class="stat-card" :class="{ 'loading': loading, 'error': loadError }">
        <div class="stat-title">待审核PR</div>
        <div v-if="loading" class="stat-value loading-placeholder"></div>
        <div v-else-if="loadError" class="stat-value error-text">加载失败</div>
        <div v-else class="stat-value">{{ stats.pull_requests.pending || 0 }}</div>
        <div v-if="loading" class="stat-desc loading-placeholder"></div>
        <div v-else-if="loadError" class="stat-desc error-text">请刷新重试</div>
        <div v-else class="stat-desc">今日新增{{ stats.pull_requests.new_today || 0 }}个</div>
      </div>
      <div class="stat-card" :class="{ 'loading': loading, 'error': loadError }">
        <div class="stat-title">待处理需求</div>
        <div v-if="loading" class="stat-value loading-placeholder"></div>
        <div v-else-if="loadError" class="stat-value error-text">加载失败</div>
        <div v-else class="stat-value">{{ stats.requirements.pending || 0 }}</div>
        <div v-if="loading" class="stat-desc loading-placeholder"></div>
        <div v-else-if="loadError" class="stat-desc error-text">请刷新重试</div>
        <div v-else class="stat-desc">逾期{{ stats.requirements.overdue || 0 }}个</div>
      </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="page-section-title">快速操作</div>
    <div class="card-grid">
      <div class="home-card" @click="goToUserManagement">
        <div class="card-icon">
          <q-icon name="people" size="24px" />
        </div>
        <h3 class="card-title">用户管理</h3>
        <p class="card-desc">添加、编辑用户，分配角色</p>
      </div>
      <div class="home-card" @click="goToProjectSettings">
        <div class="card-icon">
          <q-icon name="folder" size="24px" />
        </div>
        <h3 class="card-title">项目管理</h3>
        <p class="card-desc">创建、管理项目，分配成员</p>
      </div>
      <div class="home-card" @click="goToPullRequests">
        <div class="card-icon">
          <q-icon name="merge_type" size="24px" />
        </div>
        <h3 class="card-title">PR批量审核</h3>
        <p class="card-desc">批量审核、合并PR</p>
      </div>
    </div>

    <!-- System Tasks Section -->
    <div class="page-section-title">系统待处理任务</div>
    <div class="filter-row">
      <q-select
        v-model="projectFilter"
        :options="projectOptions"
        label="项目"
        outlined
        dense
        class="filter-select"
        emit-value
        map-options
      />
      <q-select
        v-model="typeFilter"
        :options="typeOptions"
        label="类型"
        outlined
        dense
        class="filter-select"
        emit-value
        map-options
      />
      <q-select
        v-model="statusFilter"
        :options="statusOptions"
        label="状态"
        outlined
        dense
        class="filter-select"
        emit-value
        map-options
      />
    </div>

    <div class="tabs">
      <div class="tab" :class="{ 'active': activeTab === 'all' }" @click="activeTab = 'all'">
        未完成
        <span v-if="!taskCounts.all && loading" class="tab-badge loading">...</span>
        <span v-else-if="loadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ taskCounts.all || 0 }}</span>
      </div>
      <div class="tab" :class="{ 'active': activeTab === 'pending' }" @click="activeTab = 'pending'">
        待认领
        <span v-if="!taskCounts.pending && loading" class="tab-badge loading">...</span>
        <span v-else-if="loadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ taskCounts.pending || 0 }}</span>
      </div>
      <div class="tab" :class="{ 'active': activeTab === 'developing' }" @click="activeTab = 'developing'">
        开发中
        <span v-if="!taskCounts.developing && loading" class="tab-badge loading">...</span>
        <span v-else-if="loadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ taskCounts.developing || 0 }}</span>
      </div>
      <div class="tab" :class="{ 'active': activeTab === 'testing' }" @click="activeTab = 'testing'">
        测试中
        <span v-if="!taskCounts.testing && loading" class="tab-badge loading">...</span>
        <span v-else-if="loadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ taskCounts.testing || 0 }}</span>
      </div>
      <div class="tab" :class="{ 'active': activeTab === 'validating' }" @click="activeTab = 'validating'">
        待验证
        <span v-if="!taskCounts.validating && loading" class="tab-badge loading">...</span>
        <span v-else-if="loadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ taskCounts.validating || 0 }}</span>
      </div>
    </div>

    <!-- Task Cards -->
    <div class="task-list-container">
      <transition name="fade" mode="out-in">
        <div v-if="tabLoading" key="loading" class="task-loading-overlay">
          <q-spinner color="primary" size="2em" />
          <div class="loading-text">加载中...</div>
        </div>

        <div v-else-if="tabError" key="error" class="task-error-state">
          <q-icon name="error_outline" size="2em" color="grey-7" />
          <div class="error-text">加载失败</div>
          <el-button class="retry-btn" @click="refreshTabData(activeTab)">重试</el-button>
        </div>

        <div v-else-if="currentTasks.length === 0" key="empty" class="no-tasks">
          <q-icon name="info" size="2em" color="grey-7" />
          <div class="no-tasks-text">暂无任务</div>
        </div>

        <div v-else key="tasks" class="task-cards-container">
          <transition-group name="task-list" tag="div">
            <task-card
              v-for="(task, index) in currentTasks"
              :key="`${task.id || task.requirementId}-${index}`"
              :task="task"
              :user-role="userRole"
              :current-user-id="currentUserId"
              @view="viewTaskDetail"
              @edit="editTask"
              @publish="publishTask"
              @delete="deleteTask"
              @claim="claimTask"
              @submitToTest="submitTaskToTest"
              @withdrawFromTest="withdrawTaskFromTest"
              @approveTest="approveTaskTest"
              @rejectTest="rejectTaskTest"
              @approveValidation="approveTaskValidation"
              @rejectValidation="rejectTaskValidation"
            />
          </transition-group>
        </div>
      </transition>
    </div>
  </div>

  <!-- 使用公共需求详情组件 -->
  <requirement-detail
    v-model:visible="showRequirementDetailDialog"
    :requirement-id="currentRequirementId"
  />

  <div v-if="!isAdmin">
    <div class="page-section-title">我的任务</div>

    <div class="tabs">
      <div class="tab" :class="{ 'active': activeTab === 'all' }" @click="activeTab = 'all'">
        全部
        <span v-if="!userTaskCounts.all && userLoading" class="tab-badge loading">...</span>
        <span v-else-if="userLoadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ userTaskCounts.all || 0 }}</span>
      </div>
      <div class="tab" :class="{ 'active': activeTab === 'pending' }" @click="activeTab = 'pending'">
        待认领
        <span v-if="!userTaskCounts.pending && userLoading" class="tab-badge loading">...</span>
        <span v-else-if="userLoadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ userTaskCounts.pending || 0 }}</span>
      </div>
      <div class="tab" :class="{ 'active': activeTab === 'developing' }" @click="activeTab = 'developing'">
        开发中
        <span v-if="!userTaskCounts.developing && userLoading" class="tab-badge loading">...</span>
        <span v-else-if="userLoadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ userTaskCounts.developing || 0 }}</span>
      </div>
      <div class="tab" :class="{ 'active': activeTab === 'testing' }" @click="activeTab = 'testing'">
        测试中
        <span v-if="!userTaskCounts.testing && userLoading" class="tab-badge loading">...</span>
        <span v-else-if="userLoadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ userTaskCounts.testing || 0 }}</span>
      </div>
      <div class="tab" :class="{ 'active': activeTab === 'validating' }" @click="activeTab = 'validating'">
        验证中
        <span v-if="!userTaskCounts.validating && userLoading" class="tab-badge loading">...</span>
        <span v-else-if="userLoadError" class="tab-badge error">-</span>
        <span v-else class="tab-badge">{{ userTaskCounts.validating || 0 }}</span>
      </div>
    </div>

    <!-- User Task List Container -->
    <div class="task-list-container">
      <transition name="fade" mode="out-in">
        <div v-if="userTabLoading" key="loading" class="task-loading-overlay">
          <q-spinner color="primary" size="2em" />
          <div class="loading-text">加载中...</div>
        </div>

        <div v-else-if="userTabError" key="error" class="task-error-state">
          <q-icon name="error_outline" size="2em" color="grey-7" />
          <div class="error-text">加载失败</div>
          <el-button class="retry-btn" @click="refreshUserTabData(activeTab)">重试</el-button>
        </div>

        <div v-else-if="currentUserTasks.length === 0" key="empty" class="no-tasks">
          <q-icon name="info" size="2em" color="grey-7" />
          <div class="no-tasks-text">暂无任务</div>
        </div>

        <div v-else key="tasks" class="task-cards-container">
          <transition-group name="task-list" tag="div">
            <task-card
              v-for="(task, index) in currentUserTasks"
              :key="`${task.id || task.requirementId}-${index}`"
              :task="task"
              :user-role="userRole"
              :current-user-id="currentUserId"
              @view="viewUserTaskDetail"
              @edit="editTask"
              @publish="publishTask"
              @delete="deleteTask"
              @claim="claimUserTask"
              @submitToTest="submitUserTaskToTest"
              @withdrawFromTest="withdrawUserTaskFromTest"
              @approveTest="approveUserTaskTest"
              @rejectTest="rejectUserTaskTest"
              @approveValidation="approveUserTaskValidation"
              @rejectValidation="rejectUserTaskValidation"
            />
          </transition-group>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, getCurrentInstance, watch } from 'vue';
import { useUserStore } from '../stores/user';
import { useRouter } from 'vue-router';
import { adminStatsApi } from '../api/admin';
import {
  getRequirementsList,
  getUserRequirementsList,
  claimRequirement,
  updateRequirement,
  deleteRequirement,
  publishRequirement,
  submitToTest,
  withdrawFromTest,
  withdrawFromValidation,
  approveTest,
  rejectTest,
  approveValidation,
  rejectValidation
} from '../api/project';
import { Clock } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import RequirementDetail from '../components/RequirementDetail.vue';
import TaskCard from '../components/TaskCard.vue';

const { emit } = getCurrentInstance();
const userStore = useUserStore();
const router = useRouter();

// Computed properties
const isAdmin = computed(() => userStore.isAdmin);
const userName = computed(() => userStore.userName);

// 用户角色和ID
const userRole = computed(() => {
  if (userStore.isAdmin) return 'admin';
  // 这里应该从用户信息中获取实际角色，暂时根据isAdmin判断
  return 'developer'; // 默认为开发人员
});
const currentUserId = computed(() => userStore.userId || userStore.id);

// Tab state
const activeTab = ref('all');

// Stats data
const stats = ref({
  users: { total: 0, new_last_7_days: 0 },
  projects: { total: 0, new_last_7_days: 0 },
  pull_requests: { pending: 0, new_today: 0 },
  requirements: { pending: 0, overdue: 0, my_created: 0 }
});

// Task counts for different statuses
const taskCounts = ref({
  all: 0,        // 未完成
  pending: 0,    // 待认领
  developing: 0, // 开发中
  testing: 0,    // 测试中
  validating: 0  // 待验证
});

// 非管理员任务数量统计
const userTaskCounts = ref({
  all: 0,        // 未完成
  pending: 0,    // 待认领
  developing: 0, // 开发中
  testing: 0,    // 测试中
  validating: 0  // 待验证
});

// 重构：为每个tab创建独立的数据缓存
const tabsData = ref({
  all: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  },
  pending: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  },
  developing: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  },
  testing: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  },
  validating: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  }
});

// 非管理员tab数据缓存
const userTabsData = ref({
  all: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  },
  pending: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  },
  developing: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  },
  testing: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  },
  validating: {
    tasks: [],
    loaded: false,
    loading: false,
    error: false
  }
});

// 计算当前选中tab的任务数据
const currentTabData = computed(() => tabsData.value[activeTab.value]);
const currentTasks = computed(() => currentTabData.value.tasks);
const tabLoading = computed(() => currentTabData.value.loading);
const tabError = computed(() => currentTabData.value.error);

// 非管理员当前选中tab的任务数据
const currentUserTabData = computed(() => userTabsData.value[activeTab.value]);
const currentUserTasks = computed(() => currentUserTabData.value.tasks);
const userTabLoading = computed(() => currentUserTabData.value.loading);
const userTabError = computed(() => currentUserTabData.value.error);

// 全局加载状态 - 仅用于初始化和统计数据
const loading = ref(false);
const loadError = ref(false);

// 非管理员全局加载状态
const userLoading = ref(false);
const userLoadError = ref(false);

// Filter states for admin view
const projectFilter = ref(null);
const typeFilter = ref(null);
const statusFilter = ref(null);

// Filter options
const projectOptions = [
  { label: '所有项目', value: null },
  { label: '用户中心', value: 'user' },
  { label: '支付系统', value: 'payment' },
  { label: '消息服务', value: 'message' }
];

const typeOptions = [
  { label: '所有类型', value: null },
  { label: '待审核PR', value: 'pr' },
  { label: '待分配需求', value: 'requirement' },
  { label: '待处理Bug', value: 'bug' }
];

const statusOptions = [
  { label: '所有状态', value: null },
  { label: '待分配', value: 'pending' },
  { label: '开发中', value: 'dev' },
  { label: '待测试', value: 'test' }
];

// 获取管理员首页统计数据
const fetchDashboardStats = async () => {
  loading.value = true;
  loadError.value = false;

  try {
    const response = await adminStatsApi.getDashboardStats();
    stats.value = response;

    // 初始化任务计数
    await fetchTaskCounts();

    // 获取当前选中标签的任务列表（使用缓存机制）
    await fetchTasksByStatus(activeTab.value);

    loading.value = false;
  } catch (error) {
    console.error('获取统计数据失败:', error);
    loadError.value = true;
    loading.value = false;
  }
};

// 获取各状态的任务数量
const fetchTaskCounts = async () => {
  try {
    // 获取未完成的需求总数
    const uncompleteResponse = await adminStatsApi.getRequirementsByStatus('UNCOMPLETE');
    taskCounts.value.all = uncompleteResponse.total || 0;

    // 获取待认领的需求数量
    const pendingResponse = await adminStatsApi.getRequirementsByStatus('PENDING');
    taskCounts.value.pending = pendingResponse.total || 0;

    // 获取开发中的需求数量
    const developingResponse = await adminStatsApi.getRequirementsByStatus('DEVELOPING');
    taskCounts.value.developing = developingResponse.total || 0;

    // 获取测试中的需求数量
    const testingResponse = await adminStatsApi.getRequirementsByStatus('TESTING');
    taskCounts.value.testing = testingResponse.total || 0;

    // 获取待验证的需求数量
    const validatingResponse = await adminStatsApi.getRequirementsByStatus('VALIDATING');
    taskCounts.value.validating = validatingResponse.total || 0;
  } catch (error) {
    console.error('获取任务计数失败:', error);
  }
};

// 获取非管理员各状态的任务数量
const fetchUserTaskCounts = async () => {
  try {
    // 获取未完成的需求总数
    const uncompleteResponse = await getUserRequirementsList({ uncomplete: true, page: 1, pageSize: 1 });
    userTaskCounts.value.all = uncompleteResponse.total || 0;

    // 获取待认领的需求数量（状态为PENDING）
    const pendingResponse = await getUserRequirementsList({ status: 'PENDING', page: 1, pageSize: 1 });
    userTaskCounts.value.pending = pendingResponse.total || 0;

    // 获取开发中的需求数量（状态为DEVELOPING）
    const developingResponse = await getUserRequirementsList({ status: 'DEVELOPING', page: 1, pageSize: 1 });
    userTaskCounts.value.developing = developingResponse.total || 0;

    // 获取测试中的需求数量（状态为TESTING）
    const testingResponse = await getUserRequirementsList({ status: 'TESTING', page: 1, pageSize: 1 });
    userTaskCounts.value.testing = testingResponse.total || 0;

    // 获取待验证的需求数量（状态为VALIDATING）
    const validatingResponse = await getUserRequirementsList({ status: 'VALIDATING', page: 1, pageSize: 1 });
    userTaskCounts.value.validating = validatingResponse.total || 0;
  } catch (error) {
    console.error('获取非管理员任务计数失败:', error);
  }
};

// 根据状态获取需求列表
const getRequirementsByStatus = async (status) => {
  try {
    // 使用现有的requirements-list API，添加状态筛选
    const params = {
      status: status,
      page: 1,
      pageSize: 10
    };

    if (projectFilter.value) params.project_id = projectFilter.value;
    if (typeFilter.value) params.type = typeFilter.value;

    const response = await getRequirementsList(params);
    return {
      items: response.items || [],
      total: response.total || 0
    };
  } catch (error) {
    console.error(`获取${status}状态的需求失败:`, error);
    return { items: [], total: 0 };
  }
};

// 获取当前选中标签的任务列表
const fetchTasksByStatus = async (tab) => {
  const tabData = tabsData.value[tab];

  // 如果已经加载过且没有出错，直接返回缓存的数据
  if (tabData.loaded && !tabData.error) {
    return;
  }

  // 设置当前tab的加载状态
  tabData.loading = true;
  tabData.error = false;

  try {
    let status = null;
    let isUncomplete = false;

    // 根据标签确定状态
    switch (tab) {
      case 'pending':
        status = '待处理';
        break;
      case 'developing':
        status = '开发中';
        break;
      case 'testing':
        status = '测试中';
        break;
      case 'validating':
        status = '验证中';
        break;
      case 'all':
      default:
        // 获取所有未完成的需求
        isUncomplete = true;
        break;
    }

    // 构建查询参数
    const params = {
      page: 1,
      pageSize: 10
    };

    if (status) {
      params.status = status;
    } else if (isUncomplete) {
      // 使用特殊参数标记未完成状态
      params.uncomplete = true;
    }

    if (projectFilter.value) params.project_id = projectFilter.value;
    if (typeFilter.value) params.type = typeFilter.value;

    // 调用API获取需求列表
    const response = await getRequirementsList(params);
    tabData.tasks = response.items || [];
    tabData.loaded = true;
    tabData.loading = false;
    tabData.error = false;
  } catch (error) {
    console.error('获取任务列表失败:', error);
    tabData.tasks = [];
    tabData.loaded = true;
    tabData.loading = false;
    tabData.error = true;
  }
};

// 获取非管理员当前选中标签的任务列表
const fetchUserTasksByStatus = async (tab) => {
  const tabData = userTabsData.value[tab];

  // 如果已经加载过且没有出错，直接返回缓存的数据
  if (tabData.loaded && !tabData.error) {
    return;
  }

  // 设置当前tab的加载状态
  tabData.loading = true;
  tabData.error = false;

  try {
    // 构建查询参数
    const params = {
      page: 1,
      pageSize: 10
    };

    // 根据标签确定状态
    switch (tab) {
      case 'pending':
        params.status = 'PENDING';
        break;
      case 'developing':
        params.status = 'DEVELOPING';
        break;
      case 'testing':
        params.status = 'TESTING';
        break;
      case 'validating':
        params.status = 'VALIDATING';
        break;
      case 'all':
      default:
        // 获取所有未完成的需求
        params.uncomplete = true;
        break;
    }

    // 调用API获取需求列表
    const response = await getUserRequirementsList(params);
    tabData.tasks = response.items || [];
    tabData.loaded = true;
    tabData.loading = false;
    tabData.error = false;
  } catch (error) {
    console.error('获取非管理员任务列表失败:', error);
    tabData.tasks = [];
    tabData.loaded = true;
    tabData.loading = false;
    tabData.error = true;
  }
};

// 刷新指定tab的数据
const refreshTabData = async (tab) => {
  const tabData = tabsData.value[tab];
  tabData.loaded = false;
  tabData.error = false;
  await fetchTasksByStatus(tab);
};

// 刷新所有tab的数据
const refreshAllTabs = async () => {
  Object.keys(tabsData.value).forEach(tab => {
    tabsData.value[tab].loaded = false;
    tabsData.value[tab].error = false;
  });
  await fetchTasksByStatus(activeTab.value);
};

// 监听标签变化，更新任务列表（使用缓存机制）
watch(activeTab, async (newTab) => {
  if (isAdmin.value) {
    try {
      await fetchTasksByStatus(newTab);
    } catch (error) {
      console.error('切换标签时获取任务失败:', error);
    }
  }
});

// 监听筛选条件变化，清除缓存并重新加载
watch([projectFilter, typeFilter, statusFilter], async () => {
  if (isAdmin.value) {
    try {
      await refreshAllTabs();
      await fetchTaskCounts();
    } catch (error) {
      console.error('筛选条件变化时获取任务失败:', error);
    }
  }
});

// 监听标签变化，更新任务列表（非管理员）
watch(activeTab, async (newTab) => {
  if (!isAdmin.value) {
    try {
      await fetchUserTasksByStatus(newTab);
    } catch (error) {
      console.error('非管理员切换标签时获取任务失败:', error);
    }
  }
});

onMounted(() => {
  emit('update-title', isAdmin.value ? '首页' : '首页');

  // 如果是管理员，获取统计数据
  if (isAdmin.value) {
    fetchDashboardStats();
  } else {
    // 如果是非管理员，获取用户任务数据
    fetchUserDashboardStats();
  }
});

// 获取非管理员首页统计数据
const fetchUserDashboardStats = async () => {
  userLoading.value = true;
  userLoadError.value = false;

  try {
    // 初始化任务计数
    await fetchUserTaskCounts();

    // 获取当前选中标签的任务列表
    await fetchUserTasksByStatus(activeTab.value);

    userLoading.value = false;
  } catch (error) {
    console.error('获取非管理员统计数据失败:', error);
    userLoadError.value = true;
    userLoading.value = false;
  }
};

// 刷新非管理员指定tab的数据
const refreshUserTabData = async (tab) => {
  const tabData = userTabsData.value[tab];
  tabData.loaded = false;
  tabData.error = false;
  await fetchUserTasksByStatus(tab);
};

// 认领任务
const claimUserTask = async (task) => {
  try {
    await claimRequirement(task.id || task.requirementId);
    ElMessage.success('任务认领成功');
    // 刷新当前tab数据
    await refreshUserTabData(activeTab.value);
    await fetchUserTaskCounts();
  } catch (error) {
    console.error('认领任务失败:', error);
    ElMessage.error('认领任务失败');
  }
};

// 提交测试
const submitToTest = async (task) => {
  try {
    // 这里应该调用提交测试的API，暂时用跳转到详情页替代
    router.push(`/requirements/${task.id || task.requirementId}`);
  } catch (error) {
    console.error('提交测试失败:', error);
    ElMessage.error('提交测试失败');
  }
};

// 驳回任务
const rejectTask = async (task) => {
  router.push(`/requirements/${task.id || task.requirementId}`);
};

// 通过测试
const passTest = async (task) => {
  router.push(`/requirements/${task.id || task.requirementId}`);
};

// 查看验证
const viewValidation = (task) => {
  router.push(`/requirements/${task.id || task.requirementId}`);
};

// 查看用户任务详情
const viewUserTaskDetail = (task) => {
  currentRequirementId.value = task.id || task.requirementId;
  showRequirementDetailDialog.value = true;
};



// 分配开发人员
const assignDeveloper = (task) => {
  router.push(`/requirements/${task.id || task.requirementId}`);
};

// 查看进度
const viewProgress = (task) => {
  router.push(`/requirements/${task.id || task.requirementId}`);
};

// 查看测试
const viewTesting = (task) => {
  router.push(`/requirements/${task.id || task.requirementId}`);
};

// 验证任务
const validateTask = (task) => {
  router.push(`/requirements/${task.id || task.requirementId}`);
};

// 需求详情对话框
const showRequirementDetailDialog = ref(false);
const currentRequirementId = ref(null);

// 查看任务详情
const viewTaskDetail = (task) => {
  currentRequirementId.value = task.id || task.requirementId;
  showRequirementDetailDialog.value = true;
};

// Navigation functions
const goToUserManagement = () => {
  router.push('/admin/users');
};

const goToProjectSettings = () => {
  router.push('/admin/projects');
};

const goToPullRequests = () => {
  router.push('/pull-requests');
};

// 新的任务操作事件处理方法
const editTask = (task) => {
  // 跳转到需求详情页面进行编辑
  router.push(`/requirements/${task.id || task.requirementId}`);
};

const publishTask = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要发布该草稿需求吗？发布后将创建GitLab分支并通知相关人员。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用发布需求API
    await publishRequirement(task.id || task.requirementId);
    ElMessage.success('需求已发布，状态已更新为待认领');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发布需求失败:', error);
      ElMessage.error('发布需求失败: ' + (error.message || '未知错误'));
    }
  }
};

const deleteTask = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm(
      `确定要删除需求"${task.requirementName || task.title}"吗？此操作将同时删除相关的GitLab分支，且无法恢复。`,
      '危险操作',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
        confirmButtonClass: 'el-button--danger'
      }
    );

    // 调用删除需求API
    await deleteRequirement(task.id || task.requirementId);
    ElMessage.success('需求已删除');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除需求失败:', error);
      ElMessage.error('删除需求失败: ' + (error.message || '未知错误'));
    }
  }
};

const claimTask = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要认领该需求吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用认领API
    await claimRequirement(task.id || task.requirementId);
    ElMessage.success('需求已认领，状态已更新为开发中');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('认领需求失败:', error);
      ElMessage.error('认领需求失败: ' + (error.message || '未知错误'));
    }
  }
};

const submitTaskToTest = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要将该需求提交测试吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用提交测试API
    await submitToTest(task.id || task.requirementId);
    ElMessage.success('需求状态已更新为测试中');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交测试失败:', error);
      ElMessage.error('提交测试失败: ' + (error.message || '未知错误'));
    }
  }
};

const withdrawTaskFromTest = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要撤回测试吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用撤回测试API
    await withdrawFromTest(task.id || task.requirementId);
    ElMessage.success('需求已撤回，状态已更新为开发中');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤回测试失败:', error);
      ElMessage.error('撤回测试失败: ' + (error.message || '未知错误'));
    }
  }
};

const approveTaskTest = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要通过测试吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用通过测试API
    await approveTest(task.id || task.requirementId);
    ElMessage.success('需求状态已更新为验证中');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('通过测试失败:', error);
      ElMessage.error('通过测试失败: ' + (error.message || '未知错误'));
    }
  }
};

const rejectTaskTest = async (task) => {
  try {
    // 输入驳回理由
    const { value: reason } = await ElMessageBox.prompt('请输入驳回理由', '驳回测试', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '驳回理由不能为空'
    });

    // 调用驳回测试API
    await rejectTest(task.id || task.requirementId, reason);
    ElMessage.success('需求已驳回，状态已更新为开发中');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('驳回测试失败:', error);
      ElMessage.error('驳回测试失败: ' + (error.message || '未知错误'));
    }
  }
};

const approveTaskValidation = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要通过验证吗? 通过后需求将标记为已完成。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用通过验证API
    await approveValidation(task.id || task.requirementId);
    ElMessage.success('需求验证已通过，状态已更新为已完成');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('通过验证失败:', error);
      ElMessage.error('通过验证失败: ' + (error.message || '未知错误'));
    }
  }
};

const rejectTaskValidation = async (task) => {
  try {
    // 输入驳回理由
    const { value: reason } = await ElMessageBox.prompt('请输入驳回理由', '驳回验证', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '驳回理由不能为空'
    });

    // 调用驳回验证API
    await rejectValidation(task.id || task.requirementId, reason);
    ElMessage.success('需求验证已驳回，状态已更新为测试中');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('驳回验证失败:', error);
      ElMessage.error('驳回验证失败: ' + (error.message || '未知错误'));
    }
  }
};

const withdrawTaskFromValidation = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要撤回验证吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用撤回验证API
    await withdrawFromValidation(task.id || task.requirementId);
    ElMessage.success('需求已撤回，状态已更新为测试中');

    // 刷新当前tab数据和任务计数
    await refreshTabData(activeTab.value);
    await fetchTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤回验证失败:', error);
      ElMessage.error('撤回验证失败: ' + (error.message || '未知错误'));
    }
  }
};

// 非管理员任务操作事件处理方法
const submitUserTaskToTest = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要将该需求提交测试吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用提交测试API
    await submitToTest(task.id || task.requirementId);
    ElMessage.success('需求状态已更新为测试中');

    // 刷新当前tab数据和任务计数
    await refreshUserTabData(activeTab.value);
    await fetchUserTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交测试失败:', error);
      ElMessage.error('提交测试失败: ' + (error.message || '未知错误'));
    }
  }
};

const withdrawUserTaskFromTest = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要撤回测试吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用撤回测试API
    await withdrawFromTest(task.id || task.requirementId);
    ElMessage.success('需求已撤回，状态已更新为开发中');

    // 刷新当前tab数据和任务计数
    await refreshUserTabData(activeTab.value);
    await fetchUserTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤回测试失败:', error);
      ElMessage.error('撤回测试失败: ' + (error.message || '未知错误'));
    }
  }
};

const approveUserTaskTest = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要通过测试吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用通过测试API
    await approveTest(task.id || task.requirementId);
    ElMessage.success('需求状态已更新为验证中');

    // 刷新当前tab数据和任务计数
    await refreshUserTabData(activeTab.value);
    await fetchUserTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('通过测试失败:', error);
      ElMessage.error('通过测试失败: ' + (error.message || '未知错误'));
    }
  }
};

const rejectUserTaskTest = async (task) => {
  try {
    // 输入驳回理由
    const { value: reason } = await ElMessageBox.prompt('请输入驳回理由', '驳回测试', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '驳回理由不能为空'
    });

    // 调用驳回测试API
    await rejectTest(task.id || task.requirementId, reason);
    ElMessage.success('需求已驳回，状态已更新为开发中');

    // 刷新当前tab数据和任务计数
    await refreshUserTabData(activeTab.value);
    await fetchUserTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('驳回测试失败:', error);
      ElMessage.error('驳回测试失败: ' + (error.message || '未知错误'));
    }
  }
};

const approveUserTaskValidation = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要通过验证吗? 通过后需求将标记为已完成。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用通过验证API
    await approveValidation(task.id || task.requirementId);
    ElMessage.success('需求验证已通过，状态已更新为已完成');

    // 刷新当前tab数据和任务计数
    await refreshUserTabData(activeTab.value);
    await fetchUserTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('通过验证失败:', error);
      ElMessage.error('通过验证失败: ' + (error.message || '未知错误'));
    }
  }
};

const rejectUserTaskValidation = async (task) => {
  try {
    // 输入驳回理由
    const { value: reason } = await ElMessageBox.prompt('请输入驳回理由', '驳回验证', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '驳回理由不能为空'
    });

    // 调用驳回验证API
    await rejectValidation(task.id || task.requirementId, reason);
    ElMessage.success('需求验证已驳回，状态已更新为测试中');

    // 刷新当前tab数据和任务计数
    await refreshUserTabData(activeTab.value);
    await fetchUserTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('驳回验证失败:', error);
      ElMessage.error('驳回验证失败: ' + (error.message || '未知错误'));
    }
  }
};

const withdrawUserTaskFromValidation = async (task) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm('确定要撤回验证吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    // 调用撤回验证API
    await withdrawFromValidation(task.id || task.requirementId);
    ElMessage.success('需求已撤回，状态已更新为测试中');

    // 刷新当前tab数据和任务计数
    await refreshUserTabData(activeTab.value);
    await fetchUserTaskCounts();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤回验证失败:', error);
      ElMessage.error('撤回验证失败: ' + (error.message || '未知错误'));
    }
  }
};
</script>

<style scoped>
/* Stats Header */
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stats-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.refresh-btn {
  background-color: #f0f2f5;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.refresh-btn:hover {
  background-color: #e6e8eb;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Stats Row */
.stats-row {
  display: flex;
  gap: var(--spacing-large);
  margin-bottom: 30px;
}

.stat-card {
  flex: 1;
  background-color: #fff;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-md);
  padding: 20px;
}

.stat-title {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-small);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--primary-color);
}

.stat-desc {
  font-size: 13px;
  color: var(--text-hint);
  margin-top: 4px;
}

.loading-placeholder {
  height: 1em;
  background-color: #f0f2f5;
  border-radius: 4px;
  animation: pulse 1.5s infinite;
}

.stat-value.loading-placeholder {
  height: 1.5em;
  width: 60%;
  margin: 8px 0;
}

.stat-desc.loading-placeholder {
  height: 0.8em;
  width: 80%;
}

.stat-card.error {
  border: 1px solid rgba(255, 77, 79, 0.2);
  background-color: rgba(255, 77, 79, 0.05);
}

.error-text {
  color: #ff4d4f;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

/* Section Title */
.page-section-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-medium);
  padding-bottom: var(--spacing-small);
  border-bottom: 1px solid #eee;
}

/* Card Grid */
.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-large);
  margin-bottom: 30px;
}

.home-card {
  background-color: #fff;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-large);
  text-align: center;
  transition: all 0.3s;
  cursor: pointer;
}

.home-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-medium);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-small);
}

.card-desc {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Filter Row */
.filter-row {
  display: flex;
  gap: var(--spacing-medium);
  margin-bottom: var(--spacing-large);
}

.filter-select {
  width: 180px;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-large);
}

.tab {
  padding: 0 20px;
  height: 40px;
  display: flex;
  align-items: center;
  font-size: 15px;
  color: var(--text-secondary);
  position: relative;
  cursor: pointer;
}

.tab.active {
  color: var(--primary-color);
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: -1px;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: 2px 2px 0 0;
}

.tab-badge {
  min-width: 18px;
  height: 18px;
  background-color: var(--primary-color);
  border-radius: 9px;
  color: white;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
  margin-left: 8px;
}

.tab-badge.loading {
  background-color: #d9d9d9;
  animation: pulse 1.5s infinite;
}

.tab-badge.error {
  background-color: #ff4d4f;
}

/* Task Cards */
.task-list-container {
  position: relative;
  min-height: 100px; /* 设置固定的最小高度 */
  border-radius: var(--radius-card);
  margin-bottom: var(--spacing-medium);
}

.task-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-card);
  z-index: 2;
}

.task-error-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-card);
  z-index: 2;
}

.retry-btn {
  margin-top: 16px;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.task-cards-container {
  padding: 0;
  min-height: 400px; /* 确保容器有足够高度 */
}

.no-tasks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  color: var(--text-hint);
  min-height: 100px;
}

/* 过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.task-list-enter-active, .task-list-leave-active {
  transition: all 0.3s ease;
}

.task-list-enter-from, .task-list-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.task-list-move {
  transition: transform 0.3s ease;
}





/* Responsive Adjustments */
@media (max-width: 1024px) {
  .stats-row {
    flex-wrap: wrap;
  }

  .stat-card {
    flex: 0 0 calc(50% - 12px);
  }

  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    flex: 0 0 100%;
  }
}
</style>